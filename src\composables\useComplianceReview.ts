import { ref, computed, reactive } from 'vue'
import { message } from 'ant-design-vue'
import type { 
  ReviewTask, 
  ReviewItem, 
  FilterTab, 
  RiskLevel,
  HighlightRect,
  ExportConfig
} from '@/types/compliance'
import {
  apiGetReviewTask,
  apiGetReviewItems,
  apiUpdateReviewItem,
  apiExportReviewReport,
  apiGetPdfInfo,
  apiSavePdfAnnotation,
  apiGetPdfAnnotations
} from '@/api/compliance'
import { mockData } from '@/views/home/<USER>/mock-data'

export function useComplianceReview(taskId: string) {
  // 响应式状态
  const loading = ref(false)
  const reviewTask = ref<ReviewTask | null>(null)
  const reviewItems = ref<ReviewItem[]>([])
  const selectedItem = ref<ReviewItem | null>(null)
  const activeFilter = ref<string>('all')
  
  // PDF相关状态
  const pdfUrl = ref('')
  const currentPage = ref(1)
  const zoomLevel = ref(100)
  const highlightRects = ref<HighlightRect[]>([])
  
  // 筛选标签配置
  const filterTabs = computed<FilterTab[]>(() => {
    if (!reviewTask.value) {
      return [
        { key: 'all', label: '全部', count: 0, color: '#1890ff' },
        { key: 'risk', label: '发现风险', count: 0, color: '#ff4d4f' },
        { key: 'safe', label: '未发现风险', count: 0, color: '#52c41a' },
        { key: 'na', label: '不适用', count: 0, color: '#d9d9d9' }
      ]
    }
    
    return [
      { 
        key: 'all', 
        label: '全部', 
        count: reviewTask.value.totalItems, 
        color: '#1890ff' 
      },
      { 
        key: 'risk', 
        label: '发现风险', 
        count: reviewTask.value.riskItems, 
        color: '#ff4d4f' 
      },
      { 
        key: 'safe', 
        label: '未发现风险', 
        count: reviewTask.value.safeItems, 
        color: '#52c41a' 
      },
      { 
        key: 'na', 
        label: '不适用', 
        count: reviewTask.value.naItems, 
        color: '#d9d9d9' 
      }
    ]
  })
  
  // 过滤后的审查项目
  const filteredItems = computed(() => {
    if (activeFilter.value === 'all') {
      return reviewItems.value
    }
    return reviewItems.value.filter(item => item.riskLevel === activeFilter.value)
  })
  
  // 获取审查任务详情
  const fetchReviewTask = async () => {
    try {
      loading.value = true

      // 开发环境使用模拟数据
      if (import.meta.env.VITE_ENV === 'dev') {
        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟
        reviewTask.value = mockData.reviewTask
        return
      }

      const { data, err } = await apiGetReviewTask(taskId)
      if (err) {
        message.error('获取审查任务失败')
        return
      }
      reviewTask.value = data
    } catch (error) {
      console.error('获取审查任务失败:', error)
      message.error('获取审查任务失败')
    } finally {
      loading.value = false
    }
  }
  
  // 获取审查项目列表
  const fetchReviewItems = async (params?: { riskLevel?: string }) => {
    try {
      loading.value = true

      // 开发环境使用模拟数据
      if (import.meta.env.VITE_ENV === 'dev') {
        await new Promise(resolve => setTimeout(resolve, 300))
        let items = mockData.reviewItems

        // 根据筛选条件过滤
        if (params?.riskLevel && params.riskLevel !== 'all') {
          items = items.filter(item => item.riskLevel === params.riskLevel)
        }

        reviewItems.value = items
        return
      }

      const { data, err } = await apiGetReviewItems(taskId, params)
      if (err) {
        message.error('获取审查项目失败')
        return
      }
      reviewItems.value = data.items
    } catch (error) {
      console.error('获取审查项目失败:', error)
      message.error('获取审查项目失败')
    } finally {
      loading.value = false
    }
  }
  
  // 获取PDF信息
  const fetchPdfInfo = async (fileId: string) => {
    try {
      // 开发环境使用模拟数据
      if (import.meta.env.VITE_ENV === 'dev') {
        await new Promise(resolve => setTimeout(resolve, 200))
        pdfUrl.value = mockData.reviewTask.fileUrl
        return
      }

      const { data, err } = await apiGetPdfInfo(fileId)
      if (err) {
        message.error('获取PDF信息失败')
        return
      }
      pdfUrl.value = data.fileUrl
    } catch (error) {
      console.error('获取PDF信息失败:', error)
      message.error('获取PDF信息失败')
    }
  }
  
  // 获取PDF标注
  const fetchPdfAnnotations = async () => {
    try {
      const { data, err } = await apiGetPdfAnnotations(taskId)
      if (err) {
        console.error('获取PDF标注失败')
        return
      }
      
      // 转换为高亮矩形格式
      highlightRects.value = data.map(annotation => ({
        page: annotation.pageNumber,
        x1: annotation.coordinates.x,
        y1: annotation.coordinates.y,
        x2: annotation.coordinates.x + annotation.coordinates.width,
        y2: annotation.coordinates.y + annotation.coordinates.height,
        itemId: annotation.itemId,
        color: annotation.annotationType === 'risk' ? '#ff4d4f' : '#52c41a'
      }))
    } catch (error) {
      console.error('获取PDF标注失败:', error)
    }
  }
  
  // 设置活动筛选器
  const setActiveFilter = (key: string) => {
    activeFilter.value = key
  }
  

  
  // 选择项目
  const selectItem = (item: ReviewItem) => {
    selectedItem.value = item
    
    // 如果项目有坐标信息，跳转到对应页面并高亮
    if (item.pageNumber) {
      currentPage.value = item.pageNumber
    }
    
    if (item.coordinates) {
      // 添加临时高亮
      const tempHighlight: HighlightRect = {
        page: item.pageNumber || 1,
        x1: item.coordinates.x,
        y1: item.coordinates.y,
        x2: item.coordinates.x + item.coordinates.width,
        y2: item.coordinates.y + item.coordinates.height,
        itemId: item.id,
        color: '#1890ff'
      }
      
      // 移除之前的临时高亮，添加新的
      highlightRects.value = highlightRects.value.filter(rect => rect.itemId !== 'temp')
      highlightRects.value.push({ ...tempHighlight, itemId: 'temp' })
    }
  }
  
  // 更新审查项目
  const updateReviewItem = async (itemId: string, updates: Partial<ReviewItem>) => {
    try {
      const { data, err } = await apiUpdateReviewItem(itemId, updates)
      if (err) {
        message.error('更新审查项目失败')
        return false
      }
      
      // 更新本地数据
      const index = reviewItems.value.findIndex(item => item.id === itemId)
      if (index > -1) {
        reviewItems.value[index] = { ...reviewItems.value[index], ...updates }
      }
      
      message.success('更新成功')
      return true
    } catch (error) {
      console.error('更新审查项目失败:', error)
      message.error('更新审查项目失败')
      return false
    }
  }
  
  // 导出审查报告
  const exportReport = async (config: ExportConfig) => {
    try {
      loading.value = true
      const response = await apiExportReviewReport(taskId, config)
      
      // 创建下载链接
      const blob = new Blob([response], { 
        type: config.format === 'pdf' ? 'application/pdf' : 
              config.format === 'word' ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' :
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `审查报告_${reviewTask.value?.fileName || 'report'}.${config.format}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    } finally {
      loading.value = false
    }
  }
  
  // 保存PDF标注
  const savePdfAnnotation = async (data: {
    itemId: string
    pageNumber: number
    coordinates: { x: number; y: number; width: number; height: number }
    annotationType: 'highlight' | 'note' | 'risk'
    content?: string
  }) => {
    try {
      const { err } = await apiSavePdfAnnotation({
        taskId,
        ...data
      })
      
      if (err) {
        message.error('保存标注失败')
        return false
      }
      
      message.success('标注保存成功')
      return true
    } catch (error) {
      console.error('保存标注失败:', error)
      message.error('保存标注失败')
      return false
    }
  }
  
  // 获取风险颜色
  const getRiskColor = (riskLevel: RiskLevel) => {
    const colors = {
      risk: '#ff4d4f',
      safe: '#52c41a',
      na: '#d9d9d9'
    }
    return colors[riskLevel] || '#d9d9d9'
  }
  
  // 获取风险标签
  const getRiskLabel = (riskLevel: RiskLevel) => {
    const labels = {
      risk: '发现风险',
      safe: '未发现风险',
      na: '不适用'
    }
    return labels[riskLevel] || '未知'
  }
  
  return {
    // 状态
    loading,
    reviewTask,
    reviewItems,
    selectedItem,
    activeFilter,
    pdfUrl,
    currentPage,
    zoomLevel,
    highlightRects,

    // 计算属性
    filterTabs,
    filteredItems,

    // 方法
    fetchReviewTask,
    fetchReviewItems,
    fetchPdfInfo,
    fetchPdfAnnotations,
    setActiveFilter,
    selectItem,
    updateReviewItem,
    exportReport,
    savePdfAnnotation,
    getRiskColor,
    getRiskLabel
  }
}
