<template>
  <div :class="['review-item', item.riskLevel, { 'editable': editable }]">
    <div class="header-section">
      <div class="description-box">
        <a-tag :color="getRiskColor(item.riskLevel)">
          {{ getRiskLabel(item.riskLevel) }}
        </a-tag>
        <div class="item-description"> {{ item.description }}</div>
      </div>
      <a-radio-group v-model:value="value1" option-type="button" :options="plainOptions" />
    </div> 
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { FileTextOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
import type { ReviewItem } from '@/types/compliance'

defineOptions({
  name: 'ReviewItem'
})

interface Props {
  item: ReviewItem
  editable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editable: false
})

const emit = defineEmits<{
  'update': [item: ReviewItem]
  'verify': [item: ReviewItem]
  'cancel': [item: ReviewItem]
  'like': [item: ReviewItem]
  'dislike': [item: ReviewItem]
  'open-document': [link: string]
}>()

// 编辑状态
const editingDescription = ref(false)
const editingDetails = ref(false)
const editingBasis = ref(false)
const editingSuggestion = ref(false)

// 可编辑的内容
const editableDescription = ref('')
const editableDetails = ref('')
const editableBasis = ref('')
const editableSuggestion = ref('')

// 风险颜色和标签
const getRiskColor = (riskLevel: string) => {
  const colors = {
    risk: '#ff4d4f',
    safe: '#52c41a',
    na: '#d9d9d9'
  }
  return colors[riskLevel] || '#d9d9d9'
}

const getRiskLabel = (riskLevel: string) => {
  const labels = {
    risk: '发现风险',
    safe: '未发现风险',
    na: '不适用'
  }
  return labels[riskLevel] || '未知'
}

// 编辑方法
const startEditDescription = () => {
  if (!props.editable) return
  editableDescription.value = props.item.description
  editingDescription.value = true
}

const startEditDetails = () => {
  if (!props.editable) return
  editableDetails.value = props.item.details
  editingDetails.value = true
}

const startEditBasis = () => {
  if (!props.editable) return
  editableBasis.value = props.item.legalBasis || ''
  editingBasis.value = true
}

const startEditSuggestion = () => {
  if (!props.editable) return
  editableSuggestion.value = props.item.suggestion || ''
  editingSuggestion.value = true
}

// 保存方法
const saveDescription = () => {
  editingDescription.value = false
  if (editableDescription.value !== props.item.description) {
    emit('update', { ...props.item, description: editableDescription.value })
  }
}

const saveDetails = () => {
  editingDetails.value = false
  if (editableDetails.value !== props.item.details) {
    emit('update', { ...props.item, details: editableDetails.value })
  }
}

const saveBasis = () => {
  editingBasis.value = false
  if (editableBasis.value !== props.item.legalBasis) {
    emit('update', { ...props.item, legalBasis: editableBasis.value })
  }
}

const saveSuggestion = () => {
  editingSuggestion.value = false
  if (editableSuggestion.value !== props.item.suggestion) {
    emit('update', { ...props.item, suggestion: editableSuggestion.value })
  }
}

// 操作方法
const handleSave = () => {
  // 保存所有修改
  emit('update', props.item)
}

const handleCancel = () => {
  emit('cancel', props.item)
}

const handleVerify = () => {
  emit('verify', props.item)
}

const handleLike = () => {
  emit('like', props.item)
}

const handleDislike = () => {
  emit('dislike', props.item)
}

const openDocument = () => {
  if (props.item.documentLink) {
    emit('open-document', props.item.documentLink)
  }
}
</script>

<style lang="scss" scoped>
.review-item {
  padding:12px 16px 20px 16px;
  border-bottom: 1px solid #E5E7EB;  
  background: var(--fill-0);
  margin-bottom: 12px;
  transition: all 0.2s;  
}
</style>
