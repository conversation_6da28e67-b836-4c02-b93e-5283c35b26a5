<template>
  <div :class="['review-item', item.riskLevel, { 'editable': editable }]">
    <!-- 头部区域：风险标签 + 描述 + 点赞点踩 -->
    <div class="header-section">
      <div class="content-left">
        <a-tag :class="getRiskStyle(item.reviewItemResult).className" class="tip-tag">
          {{ getRiskStyle(item.reviewItemResult).text }}
        </a-tag>
        <div class="item-description">{{ item.reviewItemName }}</div>
      </div>
      <div class="vote-section">
        <a-radio-group
          v-model:value="voteValue"
          option-type="button"
          size="small"
          @change="handleVoteChange"
        >
          <a-radio-button value="like">
            👍 {{ item.likeCount || 0 }}
          </a-radio-button>
          <a-radio-button value="dislike">
            👎 {{ item.dislikeCount || 0 }}
          </a-radio-button>
        </a-radio-group>
      </div>
    </div>
    <div v-if="item.details || item.riskDetails" class="risk-details">
      <span class="risk-label">风险提示：</span>
      <span class="risk-content">{{ item.details || item.riskDetails }}</span>
    </div>
    <div v-if="item.legalBasis" class="legal-basis">
      <span class="basis-label">审查依据：</span>
      <span class="basis-content">{{ item.legalBasis }}</span>
    </div>
    <div v-if="item.suggestion" class="suggestion">
      <span class="suggestion-label">建议修改：</span>
      <span class="suggestion-content">"{{ item.suggestion }}"</span>
    </div>
    <div class="action-buttons">
      <a-button
        v-if="item.riskLevel === 'risk'"
        type="primary"
        size="small"
        @click="handleVerify"
      >
        已修改
      </a-button>
      <a-button
        size="small"
        @click="handleCancel"
      >
        撤回
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getRiskStyle } from '@/views/home/<USER>/examine'

defineOptions({
  name: 'ReviewItem'
})

interface Props {
  item: Record<string,any>
  editable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editable: false
})

const emit = defineEmits<{
  'update': [item: Record<string,any>]
  'verify': [item: Record<string,any>]
  'cancel': [item: Record<string,any>]
  'like': [item: Record<string,any>]
  'dislike': [item: Record<string,any>]
}>()

// 投票状态
const voteValue = ref<string>('')

// 风险颜色和标签
const getRiskColor = (riskLevel: string) => {
  const colorClasses: Record<string, string> = {
    risk: 'risk-tag',
    safe: 'safe-tag',
    na: 'na-tag'
  }
  return colorClasses[riskLevel] || 'na-tag'
}

const getRiskLabel = (riskLevel: string) => {
  const labels: Record<string, string> = {
    risk: '发现风险',
    safe: '未发现风险',
    na: '不适用'
  }
  return labels[riskLevel] || '未知'
}

// 事件处理
const handleVoteChange = (e: any) => {
  const value = e.target.value
  if (value === 'like') {
    emit('like', props.item)
  } else if (value === 'dislike') {
    emit('dislike', props.item)
  }
}

const handleVerify = () => {
  emit('verify', props.item)
}

const handleCancel = () => {
  emit('cancel', props.item)
}
</script>

<style lang="scss" scoped>
.review-item {
  padding: 16px;
  border-bottom: 1px solid #E5E7EB;
  background: var(--fill-0);
  transition: all 0.2s; 
  .tip-tag { 
    font-weight: 400;
    background-color: #fafafa;
    border-color: #d9d9d9;
    color: #8c8c8c;
    &.risk {
      background-color: #fff2f0;
      border-color: #ffccc7;
      color: #ff4d4f;
    }

    &.safe{
      background-color: #F6FFED;
      border-color: #B7EB8F;
      color: #52C41A;
    } 
  }
  .header-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .content-left {
      flex: 1;
      display: flex; 
      .item-description {
        font-size: 14px;
        color: var(--text-5);
        font-weight: 500;
        line-height: 1.5;
      }
    }

    .vote-section {
      flex-shrink: 0;
      margin-left: 16px;

      :deep(.ant-radio-group) {
        .ant-radio-button-wrapper {
          border-color: #d9d9d9;
          color: #666;
          font-size: 12px;
          height: 28px;
          line-height: 26px;
          padding: 0 8px;

          &:hover {
            border-color: var(--main-6);
            color: var(--main-6);
          }

          &.ant-radio-button-wrapper-checked {
            background: var(--main-6);
            border-color: var(--main-6);
            color: white;

            &:hover {
              background: var(--main-7);
              border-color: var(--main-7);
            }
          }
        }
      }
    }
  }

  .risk-details {
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.5;

    .risk-label {
      color: var(--error-6);
      font-weight: 500;
    }

    .risk-content {
      color: var(--text-4);
    }
  }

  .legal-basis {
    margin-bottom: 12px;
    font-size: 14px;
    line-height: 1.5;

    .basis-label {
      color: var(--text-5);
      font-weight: 500;
    }

    .basis-content {
      color: var(--text-4);
    }
  }

  .suggestion {
    margin-bottom: 16px;
    padding: 8px 12px;
    background: var(--warning-1);
    border-radius: 4px;
    border-left: 3px solid var(--warning-6);
    font-size: 14px;

    .suggestion-label {
      color: var(--warning-7);
      font-weight: 500;
    }

    .suggestion-content {
      color: var(--warning-8);
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;

    .ant-btn {
      height: 28px;
      font-size: 12px;
      padding: 0 12px;
    }
  }
}
</style>
