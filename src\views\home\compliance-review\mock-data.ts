import type { ReviewTask, ReviewItem, CheckList, CheckListItem } from '@/types/compliance'

// 模拟审查任务数据
export const mockReviewTask: ReviewTask = {
  id: '1',
  taskName: '深圳市宝安区人民医院皮肤科设备采购审查',
  fileName: '深圳市宝安区人民医院皮肤科设备一批采购.pdf',
  fileUrl: '/static/sample.pdf',
  reviewTime: '2024-01-15 09:30:00',
  status: 'in_progress',
  totalItems: 16,
  riskItems: 6,
  safeItems: 5,
  naItems: 5,
  items: []
}

// 模拟审查项目数据
export const mockReviewItems: ReviewItem[] = [
  {
    id: '1',
    title: '供应商资质审查',
    description: '检查供应商的营业执照、资质证书、税务登记证等基本资料的完整性和有效性',
    details: '供应商提供的营业执照已过期，有效期至2023年12月31日，需要更新最新的证照信息。同时缺少相关的医疗器械经营许可证。',
    riskLevel: 'risk',
    documentLink: '/documents/supplier-qualification.pdf',
    pageNumber: 3,
    coordinates: {
      x: 100,
      y: 200,
      width: 300,
      height: 50
    },
    createdAt: '2024-01-15 09:30:00',
    updatedAt: '2024-01-15 10:15:00'
  },
  {
    id: '2',
    title: '技术规格符合性',
    description: '验证设备技术参数是否符合采购要求，包括性能指标、技术标准等',
    details: '设备技术参数完全符合采购需求，通过技术评审。所有关键指标均达到或超过招标文件要求。',
    riskLevel: 'safe',
    documentLink: '/documents/tech-specs.pdf',
    pageNumber: 8,
    coordinates: {
      x: 150,
      y: 300,
      width: 400,
      height: 80
    },
    createdAt: '2024-01-15 09:45:00',
    updatedAt: '2024-01-15 10:20:00'
  },
  {
    id: '3',
    title: '价格合理性分析',
    description: '分析投标价格的合理性，包括成本构成、市场价格对比等',
    details: '投标价格在合理范围内，与市场价格基本一致，成本构成清晰明确。',
    riskLevel: 'safe',
    pageNumber: 12,
    coordinates: {
      x: 80,
      y: 150,
      width: 350,
      height: 60
    },
    createdAt: '2024-01-15 10:00:00',
    updatedAt: '2024-01-15 10:25:00'
  },
  {
    id: '4',
    title: '履约能力评估',
    description: '评估供应商的履约能力，包括生产能力、交付能力、售后服务等',
    details: '供应商履约能力存在一定风险，近期有延期交付记录，需要加强监管。',
    riskLevel: 'risk',
    pageNumber: 15,
    coordinates: {
      x: 120,
      y: 250,
      width: 380,
      height: 70
    },
    createdAt: '2024-01-15 10:15:00',
    updatedAt: '2024-01-15 10:30:00'
  },
  {
    id: '5',
    title: '合同条款审查',
    description: '审查合同条款的完整性和合规性，包括付款方式、违约责任等',
    details: '合同条款基本完整，但在违约责任条款中存在表述不清的问题。',
    riskLevel: 'risk',
    pageNumber: 20,
    createdAt: '2024-01-15 10:30:00',
    updatedAt: '2024-01-15 10:45:00'
  },
  {
    id: '6',
    title: '环保要求符合性',
    description: '检查设备是否符合环保要求和相关标准',
    details: '该项目不涉及环保要求，标记为不适用。',
    riskLevel: 'na',
    createdAt: '2024-01-15 10:45:00'
  }
]

// 模拟审查清单数据
export const mockCheckList: CheckList = {
  id: 'checklist-1',
  name: '政府采购合规性审查清单',
  version: 'v2.1',
  categories: ['资质审查', '技术评审', '商务评审', '合同审查', '其他事项'],
  items: [
    {
      id: 'item-1',
      category: '资质审查',
      title: '营业执照有效性',
      description: '检查供应商营业执照是否在有效期内',
      required: true,
      completed: false,
      riskLevel: 'risk',
      notes: '营业执照已过期，需要更新'
    },
    {
      id: 'item-2',
      category: '资质审查',
      title: '相关资质证书',
      description: '检查是否具备相关行业资质证书',
      required: true,
      completed: true,
      riskLevel: 'safe'
    },
    {
      id: 'item-3',
      category: '技术评审',
      title: '技术参数符合性',
      description: '验证设备技术参数是否符合要求',
      required: true,
      completed: true,
      riskLevel: 'safe'
    },
    {
      id: 'item-4',
      category: '技术评审',
      title: '技术方案可行性',
      description: '评估技术方案的可行性和先进性',
      required: true,
      completed: false
    },
    {
      id: 'item-5',
      category: '商务评审',
      title: '价格合理性',
      description: '分析投标价格的合理性',
      required: true,
      completed: true,
      riskLevel: 'safe'
    },
    {
      id: 'item-6',
      category: '商务评审',
      title: '付款条件',
      description: '检查付款条件是否合理',
      required: false,
      completed: false
    },
    {
      id: 'item-7',
      category: '合同审查',
      title: '合同条款完整性',
      description: '检查合同条款是否完整',
      required: true,
      completed: false,
      riskLevel: 'risk',
      notes: '部分条款表述不清'
    },
    {
      id: 'item-8',
      category: '合同审查',
      title: '违约责任条款',
      description: '检查违约责任条款是否明确',
      required: true,
      completed: false
    },
    {
      id: 'item-9',
      category: '其他事项',
      title: '环保要求',
      description: '检查是否符合环保要求',
      required: false,
      completed: true,
      riskLevel: 'na',
      notes: '该项目不涉及环保要求'
    },
    {
      id: 'item-10',
      category: '其他事项',
      title: '安全要求',
      description: '检查是否符合安全要求',
      required: false,
      completed: false
    }
  ],
  createdAt: '2024-01-15 08:00:00',
  updatedAt: '2024-01-15 10:45:00'
}

// 模拟PDF标注数据
export const mockPdfAnnotations = [
  {
    id: 'annotation-1',
    itemId: '1',
    pageNumber: 3,
    coordinates: {
      x: 100,
      y: 200,
      width: 300,
      height: 50
    },
    annotationType: 'risk',
    content: '供应商资质存在问题',
    createdAt: '2024-01-15 09:30:00'
  },
  {
    id: 'annotation-2',
    itemId: '2',
    pageNumber: 8,
    coordinates: {
      x: 150,
      y: 300,
      width: 400,
      height: 80
    },
    annotationType: 'safe',
    content: '技术规格符合要求',
    createdAt: '2024-01-15 09:45:00'
  }
]

// 导出所有模拟数据
export const mockData = {
  reviewTask: mockReviewTask,
  reviewItems: mockReviewItems,
  checkList: mockCheckList,
  pdfAnnotations: mockPdfAnnotations
}
