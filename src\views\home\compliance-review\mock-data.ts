import type { ReviewTask, ReviewItem, CheckList, CheckListItem } from '@/types/compliance'

// 模拟审查任务数据
export const mockReviewTask: ReviewTask = {
  id: '1',
  taskName: '深圳市宝安区人民医院皮肤科设备采购审查',
  fileName: '深圳市宝安区人民医院皮肤科设备一批采购.pdf',
  fileUrl: '/static/sample.pdf',
  reviewTime: '2024-01-15 09:30:00',
  status: 'in_progress',
  totalItems: 16,
  riskItems: 6,
  safeItems: 5,
  naItems: 5,
  items: []
}

// 模拟审查项目数据
export const mockReviewItems: ReviewItem[] = [
  {
    id: '1',
    title: '资格公平性检查',
    description: '不得非法限定供应商所有制形式、组织形式、注册地或所在地',
    details: '条款"投标单位须为有营销资质企业"限定了供应商的所有制形式，可能构成对潜在供应商的差别待遇。',
    riskLevel: 'risk',
    documentLink: '/documents/supplier-qualification.pdf',
    pageNumber: 3,
    subItemCount: 4,
    legalBasis: '《中华人民共和国政府采购法》第二十二条：采购人可以根据采购项目的特殊要求，规定供应商的特定条件，但不得以不合理的条件对供应商实行差别待遇或者歧视待遇。',
    suggestion: '投标单位不得限企业性质制',
    likeCount: 0,
    dislikeCount: 0,
    subItems: [
      {
        id: '1-1',
        description: '不得非法限定供应商所有制形式、组织形式、注册地或所在地',
        riskLevel: 'risk',
        riskDetails: '条款"投标单位须为有营销资质企业"限定了供应商的所有制形式，可能构成对潜在供应商的差别待遇。',
        legalBasis: '《中华人民共和国政府采购法》第二十二条：采购人可以根据采购项目的特殊要求，规定供应商的特定条件，但不得以不合理的条件对供应商实行差别待遇或者歧视待遇。',
        suggestion: '投标单位不得限企业性质制',
        likeCount: 0,
        dislikeCount: 0
      }
    ],
    createdAt: '2024-01-15 09:30:00',
    updatedAt: '2024-01-15 10:15:00'
  },
  {
    id: '2',
    title: '需求公平性',
    description: '不得将供应商规模条件、股票结构、年限设置为资格条件',
    details: '条款"公司团队规模在50人以上"限定了供应商，可能构成对潜在供应商的差别待遇。',
    riskLevel: 'risk',
    documentLink: '/documents/tech-specs.pdf',
    pageNumber: 8,
    subItemCount: 3,
    legalBasis: '《中华人民共和国政府采购法》第二十二条：任何单位和个人不得采用任何方式，阻挠和限制供应商自由进入本地区和本行业的政府采购市场。',
    suggestion: '公司团队规模不得数量限制',
    likeCount: 0,
    dislikeCount: 0,
    coordinates: {
      x: 150,
      y: 300,
      width: 400,
      height: 80
    },
    createdAt: '2024-01-15 09:45:00',
    updatedAt: '2024-01-15 10:20:00'
  },
  {
    id: '3',
    title: '未发现风险',
    description: '不得将经营网点、现场勘察作为合格供应商资格条件',
    details: '未发现相关不合规管理性条款，现场勘察作为合格供应商资格条件',
    riskLevel: 'safe',
    pageNumber: 12,
    subItemCount: 3,
    legalBasis: '《深圳经济特区政府采购监督管理条例》第三十四条：投标资本、资产净额、营业收入、从业人员、利润、纳税额等规模条件作为资格条件，但法律法规另有规定的除外。',
    suggestion: '',
    likeCount: 0,
    dislikeCount: 0,
    coordinates: {
      x: 80,
      y: 150,
      width: 350,
      height: 60
    },
    createdAt: '2024-01-15 10:00:00',
    updatedAt: '2024-01-15 10:25:00'
  },
  {
    id: '4',
    title: '履约能力评估',
    description: '评估供应商的履约能力，包括生产能力、交付能力、售后服务等',
    details: '供应商履约能力存在一定风险，近期有延期交付记录，需要加强监管。',
    riskLevel: 'risk',
    pageNumber: 15,
    coordinates: {
      x: 120,
      y: 250,
      width: 380,
      height: 70
    },
    createdAt: '2024-01-15 10:15:00',
    updatedAt: '2024-01-15 10:30:00'
  },
  {
    id: '5',
    title: '合同条款审查',
    description: '审查合同条款的完整性和合规性，包括付款方式、违约责任等',
    details: '合同条款基本完整，但在违约责任条款中存在表述不清的问题。',
    riskLevel: 'risk',
    pageNumber: 20,
    createdAt: '2024-01-15 10:30:00',
    updatedAt: '2024-01-15 10:45:00'
  },
  {
    id: '6',
    title: '环保要求符合性',
    description: '检查设备是否符合环保要求和相关标准',
    details: '该项目不涉及环保要求，标记为不适用。',
    riskLevel: 'na',
    createdAt: '2024-01-15 10:45:00'
  }
]

// 模拟审查清单数据
export const mockCheckList: CheckList = {
  id: 'checklist-1',
  name: '政府采购合规性审查清单',
  version: 'v2.1',
  categories: ['资质审查', '技术评审', '商务评审', '合同审查', '其他事项'],
  items: [
    {
      id: 'item-1',
      category: '资质审查',
      title: '营业执照有效性',
      description: '检查供应商营业执照是否在有效期内',
      required: true,
      completed: false,
      riskLevel: 'risk',
      notes: '营业执照已过期，需要更新'
    },
    {
      id: 'item-2',
      category: '资质审查',
      title: '相关资质证书',
      description: '检查是否具备相关行业资质证书',
      required: true,
      completed: true,
      riskLevel: 'safe'
    },
    {
      id: 'item-3',
      category: '技术评审',
      title: '技术参数符合性',
      description: '验证设备技术参数是否符合要求',
      required: true,
      completed: true,
      riskLevel: 'safe'
    },
    {
      id: 'item-4',
      category: '技术评审',
      title: '技术方案可行性',
      description: '评估技术方案的可行性和先进性',
      required: true,
      completed: false
    },
    {
      id: 'item-5',
      category: '商务评审',
      title: '价格合理性',
      description: '分析投标价格的合理性',
      required: true,
      completed: true,
      riskLevel: 'safe'
    },
    {
      id: 'item-6',
      category: '商务评审',
      title: '付款条件',
      description: '检查付款条件是否合理',
      required: false,
      completed: false
    },
    {
      id: 'item-7',
      category: '合同审查',
      title: '合同条款完整性',
      description: '检查合同条款是否完整',
      required: true,
      completed: false,
      riskLevel: 'risk',
      notes: '部分条款表述不清'
    },
    {
      id: 'item-8',
      category: '合同审查',
      title: '违约责任条款',
      description: '检查违约责任条款是否明确',
      required: true,
      completed: false
    },
    {
      id: 'item-9',
      category: '其他事项',
      title: '环保要求',
      description: '检查是否符合环保要求',
      required: false,
      completed: true,
      riskLevel: 'na',
      notes: '该项目不涉及环保要求'
    },
    {
      id: 'item-10',
      category: '其他事项',
      title: '安全要求',
      description: '检查是否符合安全要求',
      required: false,
      completed: false
    }
  ],
  createdAt: '2024-01-15 08:00:00',
  updatedAt: '2024-01-15 10:45:00'
}

// 模拟PDF标注数据
export const mockPdfAnnotations = [
  {
    id: 'annotation-1',
    itemId: '1',
    pageNumber: 3,
    coordinates: {
      x: 100,
      y: 200,
      width: 300,
      height: 50
    },
    annotationType: 'risk',
    content: '供应商资质存在问题',
    createdAt: '2024-01-15 09:30:00'
  },
  {
    id: 'annotation-2',
    itemId: '2',
    pageNumber: 8,
    coordinates: {
      x: 150,
      y: 300,
      width: 400,
      height: 80
    },
    annotationType: 'safe',
    content: '技术规格符合要求',
    createdAt: '2024-01-15 09:45:00'
  }
]

// 导出所有模拟数据
export const mockData = {
  reviewTask: mockReviewTask,
  reviewItems: mockReviewItems,
  checkList: mockCheckList,
  pdfAnnotations: mockPdfAnnotations
}
