# 合规性审查页面

## 功能概述

合规性审查页面是一个专门用于文档合规性审查的工具页面，主要用于政府采购、招投标等场景下的文档审查工作。

## 主要功能

### 1. 页面布局
- **顶部导航区域**：包含面包屑导航和操作按钮
- **主体内容区域**：左右分栏布局，左侧为PDF阅读器，右侧为审查结果面板

### 2. PDF文档阅读
- 使用复用的 `PdfReader` 组件进行PDF文档预览
- 支持文档缩放、页面导航等基本功能
- 支持高亮显示和标注功能
- 可以根据审查项目自动定位到相关页面

### 3. 审查结果管理
- **筛选功能**：按风险等级筛选审查项目
  - 全部：显示所有审查项目
  - 发现风险：显示有风险的项目（红色标识）
  - 未发现风险：显示安全的项目（绿色标识）
  - 不适用：显示不适用的项目（灰色标识）
- **项目展示**：每个审查项目包含标题、描述、详细信息等
- **交互功能**：支持展开/收起、编辑、定位等操作

### 4. 审查清单
- 提供完整的审查清单弹窗
- 支持分类展示和进度跟踪
- 可以标记完成状态和添加备注

### 5. 导出功能
- 支持导出审查报告
- 可选择不同的导出格式（PDF、Word、Excel）
- 可配置导出内容（包含详情、图片等）

## 技术实现

### 组件结构
```
src/views/home/<USER>/
├── index.vue                    # 主页面组件
├── components/
│   └── CheckListModal.vue       # 审查清单弹窗组件
└── README.md                    # 说明文档
```

### 相关文件
```
src/
├── types/compliance.ts          # 类型定义
├── api/compliance.ts            # API接口
├── composables/useComplianceReview.ts  # 业务逻辑组合函数
└── components/PdfReader/        # PDF阅读器组件（复用）
```

### 主要依赖
- Vue 3 + TypeScript
- Ant Design Vue
- PdfReader 组件
- 自定义 Composables

## 使用方法

### 路由访问
```
/compliance-review?taskId=xxx
```

### 参数说明
- `taskId`: 审查任务ID，用于获取相关的审查数据

### API接口
主要使用以下API接口：
- `apiGetReviewTask`: 获取审查任务详情
- `apiGetReviewItems`: 获取审查项目列表
- `apiGetPdfInfo`: 获取PDF文件信息
- `apiExportReviewReport`: 导出审查报告
- `apiGetCheckList`: 获取审查清单

## 样式规范

### 设计原则
1. 遵循项目整体设计规范
2. 使用CSS变量进行主题适配
3. 响应式布局，适配不同屏幕尺寸
4. 保持组件复用性和一致性

### 颜色规范
- 风险项目：使用 `--error-6` 红色系
- 安全项目：使用 `--success-6` 绿色系
- 不适用项目：使用 `--neutral-6` 灰色系
- 主要操作：使用 `--main-6` 蓝色系

### 布局规范
- 使用弹性布局（Flexbox）
- 左侧PDF阅读器占据剩余空间
- 右侧审查面板固定宽度400px
- 合理的内边距和间距

## 开发注意事项

### 1. 数据流
- 使用 `useComplianceReview` 组合函数管理状态
- 通过 props 和 emit 进行组件通信
- 使用 computed 进行数据计算和过滤

### 2. 性能优化
- 使用 `v-if` 和 `v-show` 合理控制渲染
- 大列表使用虚拟滚动（如需要）
- 图片和文件懒加载

### 3. 错误处理
- API调用统一错误处理
- 用户友好的错误提示
- 加载状态的合理展示

### 4. 可访问性
- 合理的语义化标签
- 键盘导航支持
- 屏幕阅读器友好

## 扩展功能

### 可能的扩展方向
1. **批量操作**：支持批量修改审查项目状态
2. **协作功能**：多人协作审查，实时同步
3. **模板管理**：自定义审查清单模板
4. **历史记录**：审查过程的版本控制
5. **智能推荐**：基于AI的风险识别和建议

### 集成建议
1. **消息通知**：集成消息推送系统
2. **权限控制**：基于角色的权限管理
3. **审计日志**：完整的操作记录
4. **数据分析**：审查结果的统计分析

## 维护说明

### 代码维护
- 定期更新依赖包
- 保持代码风格一致
- 及时修复已知问题

### 功能维护
- 根据用户反馈优化交互
- 持续改进性能表现
- 适配新的业务需求
