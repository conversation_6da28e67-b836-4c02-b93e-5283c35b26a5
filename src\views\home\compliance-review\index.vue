<template>
  <div class="compliance-review-container">
    <!-- 顶部导航区域 -->
    <div class="header-section">
      <div class="breadcrumb-area">
        <div class="nav-buttons">
          <a-button type="text" class="nav-btn" @click="goHome">
            <template #icon>
              <HomeOutlined />
            </template>
            首页
          </a-button>
          <a-button type="text" class="nav-btn back-btn" @click="goBack">
            <template #icon>
              <ArrowLeftOutlined />
            </template>
          </a-button>
        </div>
        <div class="file-name">{{ fileName }}</div>
      </div>
      
      <div class="info-actions">
        <div class="review-time">
          <CalendarOutlined />
          <span>审查时间：{{ reviewTime }}</span>
        </div>
        <div class="action-buttons">
          <a-button class="export-btn" @click="handleExport">
            <template #icon>
              <DownloadOutlined />
            </template>
            导出
          </a-button>
          <a-button type="primary" @click="showCheckList">
            查看审查清单
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- PDF阅读器区域 -->
      <div class="pdf-viewer-section">
        <div class="pdf-toolbar">
          <!-- PDF工具栏按钮 -->
          <div class="toolbar-buttons">
            <a-button size="small" class="toolbar-btn">
              <template #icon>
                <ZoomInOutlined />
              </template>
            </a-button>
            <a-button size="small" class="toolbar-btn">
              <template #icon>
                <ZoomOutOutlined />
              </template>
            </a-button>
            <!-- 更多工具栏按钮... -->
            <a-select v-model:value="fontFamily" size="small" class="font-select">
              <a-select-option value="宋体">宋体</a-select-option>
              <a-select-option value="微软雅黑">微软雅黑</a-select-option>
            </a-select>
            <a-select v-model:value="fontSize" size="small" class="font-size-select">
              <a-select-option value="12">12</a-select-option>
              <a-select-option value="14">14</a-select-option>
              <a-select-option value="16">16</a-select-option>
            </a-select>
          </div>
        </div>
        
        <!-- PDF阅读器 -->
        <div class="pdf-reader-wrapper">
          <PdfReader
            v-if="pdfUrl"
            :url="pdfUrl"
            :page="currentPage"
            :zoom="zoomLevel"
            :rect="highlightRects"
            @load="onPdfLoad"
          />
          <div v-else class="pdf-placeholder">
            <BaseEmpty description="PDF文档加载中..." />
          </div>
        </div>
      </div>

      <!-- 审查结果面板 -->
      <div class="review-panel">
        <div class="panel-header">
          <h3>审查结果</h3>
        </div>
        
        <!-- 筛选标签 -->
        <div class="filter-tabs">
          <a-button 
            v-for="tab in filterTabs" 
            :key="tab.key"
            :type="activeFilter === tab.key ? 'primary' : 'default'"
            :class="['filter-tab', tab.key]"
            @click="setActiveFilter(tab.key)"
          >
            {{ tab.label }}
            <a-badge :count="tab.count" :color="tab.color" />
          </a-button>
        </div>

        <!-- 审查项目列表 -->
        <div class="review-items">
          <a-spin :spinning="loading">
            <div v-if="filteredItems.length === 0" class="empty-state">
              <BaseEmpty description="暂无审查项目" />
            </div>
            <div v-else class="items-list">
              <div 
                v-for="item in filteredItems" 
                :key="item.id"
                :class="['review-item', item.riskLevel]"
                @click="selectItem(item)"
              >
                <div class="item-header">
                  <div class="risk-badge">
                    <a-tag :color="getRiskColor(item.riskLevel)">
                      {{ getRiskLabel(item.riskLevel) }}
                    </a-tag>
                  </div>
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-actions">
                    <a-button type="text" size="small" @click.stop="toggleExpand(item.id)">
                      <template #icon>
                        <DownOutlined v-if="!expandedItems.includes(item.id)" />
                        <UpOutlined v-else />
                      </template>
                    </a-button>
                  </div>
                </div>
                
                <div class="item-description">{{ item.description }}</div>
                
                <!-- 展开内容 -->
                <div v-if="expandedItems.includes(item.id)" class="item-details">
                  <div class="detail-content">{{ item.details }}</div>
                  <div v-if="item.documentLink" class="document-link">
                    <a-button type="link" @click.stop="openDocument(item.documentLink)">
                      <template #icon>
                        <FileTextOutlined />
                      </template>
                      查看相关文档
                    </a-button>
                  </div>
                  <div class="action-buttons">
                    <a-button size="small" @click.stop="handleItemAction(item, 'edit')">
                      <template #icon>
                        <EditOutlined />
                      </template>
                      编辑
                    </a-button>
                    <a-button size="small" @click.stop="handleItemAction(item, 'locate')">
                      <template #icon>
                        <AimOutlined />
                      </template>
                      定位
                    </a-button>
                  </div>
                </div>
              </div>
            </div>
          </a-spin>
        </div>
      </div>
    </div>

    <!-- 审查清单弹窗 -->
    <CheckListModal
      v-model:open="checkListVisible"
      :task-id="taskId"
      @save="handleCheckListSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  HomeOutlined,
  ArrowLeftOutlined,
  CalendarOutlined,
  DownloadOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  DownOutlined,
  UpOutlined,
  FileTextOutlined,
  EditOutlined,
  AimOutlined
} from '@ant-design/icons-vue'
import PdfReader from '@/components/PdfReader/index.vue'
import BaseEmpty from '@/components/BaseEmpty/index.vue'
import CheckListModal from './components/CheckListModal.vue'
import { useComplianceReview } from '@/composables/useComplianceReview'
import type { ExportConfig, CheckList } from '@/types/compliance'

defineOptions({
  name: 'ComplianceReview'
})

const router = useRouter()
const route = useRoute()

// 获取任务ID
const taskId = ref(route.query.taskId as string || '1')

// 使用合规性审查组合式函数
const {
  loading,
  reviewTask,
  reviewItems,
  expandedItems,
  selectedItem,
  activeFilter,
  pdfUrl,
  currentPage,
  zoomLevel,
  highlightRects,
  filterTabs,
  filteredItems,
  fetchReviewTask,
  fetchReviewItems,
  fetchPdfInfo,
  fetchPdfAnnotations,
  setActiveFilter,
  toggleExpand,
  selectItem,
  updateReviewItem,
  exportReport,
  getRiskColor,
  getRiskLabel
} = useComplianceReview(taskId.value)

// PDF工具栏相关
const fontFamily = ref('宋体')
const fontSize = ref('12')

// 弹窗状态
const checkListVisible = ref(false)

// 计算属性
const fileName = computed(() => reviewTask.value?.fileName || '文档名称')
const reviewTime = computed(() => reviewTask.value?.reviewTime || '2024-01-15 09:30:00')

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}

const handleExport = async () => {
  try {
    const config: ExportConfig = {
      format: 'pdf',
      includeDetails: true,
      includeImages: true
    }
    await exportReport(config)
  } catch (error) {
    console.error('导出失败:', error)
  }
}

const showCheckList = () => {
  checkListVisible.value = true
}

const handleCheckListSave = (checkList: CheckList) => {
  console.log('保存审查清单:', checkList)
  message.success('审查清单已保存')
}

const openDocument = (link: string) => {
  // 在新窗口打开相关文档
  window.open(link, '_blank')
}

const handleItemAction = async (item: any, action: string) => {
  switch (action) {
    case 'edit':
      // 编辑项目
      message.info('编辑功能开发中...')
      break
    case 'locate':
      // 定位到PDF中的位置
      if (item.pageNumber) {
        currentPage.value = item.pageNumber
        selectItem(item)
      }
      break
    default:
      console.log('未知操作:', action)
  }
}

const onPdfLoad = () => {
  console.log('PDF加载完成')
  // PDF加载完成后获取标注信息
  fetchPdfAnnotations()
}

// 初始化数据
const initData = async () => {
  try {
    await Promise.all([
      fetchReviewTask(),
      fetchReviewItems(),
      fetchPdfInfo(taskId.value) // 这里应该传入实际的文件ID
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    message.error('加载数据失败')
  }
}

onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.compliance-review-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--fill-0);
}

.header-section {
  padding: 16px 24px;
  border-bottom: 1px solid var(--line-2);
  background: var(--fill-0);
  
  .breadcrumb-area {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    .nav-buttons {
      display: flex;
      gap: 8px;
      margin-right: 16px;
      
      .nav-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border: 1px solid var(--line-3);
        border-radius: 4px;
        
        &.back-btn {
          min-width: auto;
          padding: 4px;
        }
      }
    }
    
    .file-name {
      font-size: var(--font-14);
      color: var(--text-4);
      font-weight: 500;
    }
  }
  
  .info-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .review-time {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--text-3);
      font-size: var(--font-14);
    }
    
    .action-buttons {
      display: flex;
      gap: 12px;
      
      .export-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        border: 1px solid var(--line-3);
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.pdf-viewer-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--line-2);
  background: var(--fill-0);

  .pdf-toolbar {
    padding: 8px 16px;
    border-bottom: 1px solid var(--line-2);
    background: var(--fill-0);
    box-shadow: 0 2px 4px -2px rgba(0, 0, 0, 0.1);

    .toolbar-buttons {
      display: flex;
      align-items: center;
      gap: 8px;

      .toolbar-btn {
        min-width: 32px;
        height: 32px;
        padding: 0;
        border: 1px solid var(--line-3);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          border-color: var(--main-6);
          color: var(--main-6);
        }
      }

      .font-select,
      .font-size-select {
        width: 80px;

        :deep(.ant-select-selector) {
          border-radius: 4px;
        }
      }
    }
  }

  .pdf-reader-wrapper {
    flex: 1;
    background: var(--fill-1);
    position: relative;

    .pdf-placeholder {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.review-panel {
  width: 400px;
  display: flex;
  flex-direction: column;
  background: var(--fill-0);
  
  .panel-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--line-2);
    
    h3 {
      margin: 0;
      font-size: var(--font-16);
      font-weight: 500;
      color: var(--text-5);
    }
  }
  
  .filter-tabs {
    padding: 16px 24px;
    display: flex;
    gap: 8px;
    border-bottom: 1px solid var(--line-2);
    background: var(--fill-1);

    .filter-tab {
      display: flex;
      align-items: center;
      gap: 4px;
      border-radius: 6px;
      padding: 6px 12px;
      transition: all 0.2s;

      :deep(.ant-badge) {
        .ant-badge-count {
          font-size: 12px;
          min-width: 18px;
          height: 18px;
          line-height: 18px;
          padding: 0 4px;
        }
      }

      &.risk {
        &.ant-btn-primary {
          background: var(--error-1);
          border-color: var(--error-6);
          color: var(--error-6);

          &:hover {
            background: var(--error-2);
          }
        }
      }

      &.safe {
        &.ant-btn-primary {
          background: var(--success-1);
          border-color: var(--success-6);
          color: var(--success-6);

          &:hover {
            background: var(--success-2);
          }
        }
      }

      &.na {
        &.ant-btn-primary {
          background: var(--neutral-1);
          border-color: var(--neutral-6);
          color: var(--neutral-6);

          &:hover {
            background: var(--neutral-2);
          }
        }
      }
    }
  }
  
  .review-items {
    flex: 1;
    overflow-y: auto;
    
    .items-list {
      padding: 16px 24px;
    }
    
    .review-item {
      padding: 16px;
      margin-bottom: 12px;
      border: 1px solid var(--line-2);
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s;
      
      &:hover {
        border-color: var(--main-6);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      &.risk {
        border-left: 4px solid var(--error-6);
      }
      
      &.safe {
        border-left: 4px solid var(--success-6);
      }
      
      &.na {
        border-left: 4px solid var(--neutral-6);
      }
      
      .item-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        
        .item-title {
          flex: 1;
          font-weight: 500;
          color: var(--text-5);
        }
      }
      
      .item-description {
        color: var(--text-3);
        font-size: var(--font-14);
        line-height: 1.5;
        margin-bottom: 8px;
      }
      
      .item-details {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid var(--line-2);
        
        .detail-content {
          color: var(--text-4);
          font-size: var(--font-14);
          line-height: 1.5;
          margin-bottom: 12px;
        }
        
        .document-link {
          margin-bottom: 12px;
        }
        
        .action-buttons {
          display: flex;
          gap: 8px;
        }
      }
    }
  }
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}
</style>
