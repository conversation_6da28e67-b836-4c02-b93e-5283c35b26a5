<template>
  <div class="compliance-review-container">
    <!-- 顶部导航区域 -->
    <div class="header-section">
      <div class="breadcrumb-area">
        <div class="nav-buttons">
          <a-button type="text" class="nav-btn back-btn" @click="goHome">
            <template #icon>
              <CornerUpLeft class="icon" :size="16"/>
            </template>
            首页
          </a-button>
          <a-button type="text" class="nav-btn " @click="goBack">
            <template #icon>
              <Clock8 class="icon" :size="16"/>
            </template>
          </a-button>
        </div>
        <div class="file-name">{{ fileName }}</div>
      </div>
      
      <div class="info-actions">
        <div class="review-time">
          <Calendar1  class="icon" :size="16"/>
          <span>审查时间：{{ reviewTime }}</span>
        </div>
        <div class="action-buttons">
          <a-button class="export-btn" @click="handleExport">
            <template #icon>
              <Download class="icon" :size="16"/>
            </template>
            导出
          </a-button>
          <a-button type="primary"  @click="showCheckList">
            查看审查清单
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- PDF阅读器区域 -->  
      <div class="pdf-reader-wrapper">
        <PdfReader
          v-if="pdfUrl"
          :url="pdfUrl"
          :page="currentPage"
          :zoom="zoomLevel"
          :rect="highlightRects"
          @load="onPdfLoad"
        />
        <div v-else class="pdf-placeholder">
          <BaseEmpty description="PDF文档加载中..." />
        </div>
      </div> 

      <!-- 审查结果面板 -->
      <div class="review-panel">
        <div class="panel-header">审查结果</div> 
        <div class="filter-tabs">
          <div
            v-for="tab in filterTabs"
            :key="tab.key"
            :class="['filter-tab', tab.key, { active: activeFilter === tab.key }]"
            @click="setActiveFilter(tab.key)"
          >
            <span class="tab-label">{{ tab.label }}</span>
            <span class="tab-count" :class="[{ active: activeFilter === tab.key }]">{{ tab.count }}</span>
          </div>
        </div>

        <!-- 审查项目列表 -->
        <div class="review-items">
          <a-spin :spinning="loading">
            <div v-if="filteredItems.length === 0" class="empty-state">
              <BaseEmpty description="暂无审查项目" />
            </div>
            <div v-else class="items-list">
              <!-- 项目标题栏 -->
              <div
                v-for="item in filteredItems"
                :key="item.id"
                class="item-group"
              >
                <div class="item-title-bar"> 
                  <span class="item-index"></span>
                  <span class="item-title">{{ item.title }}</span>
                  <span class="item-count">{{ item.subItemCount || 4 }}</span> 
                </div>

                <!-- 审查项目列表 -->
                <div class="sub-items">
                  <ReviewItem
                    v-for="subItem in item.subItems || [item]"
                    :key="subItem.id || item.id"
                    :item="subItem"
                    :editable="editingItems.includes(subItem.id || item.id)"
                    @update="handleItemUpdate"
                    @verify="handleItemVerify"
                    @cancel="handleItemCancel"
                    @like="handleItemLike"
                    @dislike="handleItemDislike"
                    @open-document="openDocument"
                  />
                </div>
              </div>
            </div>
          </a-spin>
        </div>
      </div>
    </div>

    <!-- 审查清单弹窗 -->
    <CheckListModal
      v-model:open="checkListVisible"
      :task-id="taskId"
      @save="handleCheckListSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import PdfReader from '@/components/PdfReader/index.vue'
import BaseEmpty from '@/components/BaseEmpty/index.vue'
import CheckListModal from './components/CheckListModal.vue'
import ReviewItem from './components/ReviewItem.vue'
import { useComplianceReview } from '@/composables/useComplianceReview'
import type { ExportConfig, CheckList } from '@/types/compliance'

defineOptions({
  name: 'ComplianceReview'
})

const router = useRouter()
const route = useRoute()

// 获取任务ID
const taskId = ref(route.query.taskId as string || '1')

// 使用合规性审查组合式函数
const {
  loading,
  reviewTask,
  reviewItems,
  selectedItem,
  activeFilter,
  pdfUrl,
  currentPage,
  zoomLevel,
  highlightRects,
  filterTabs,
  filteredItems,
  fetchReviewTask,
  fetchReviewItems,
  fetchPdfInfo,
  fetchPdfAnnotations,
  setActiveFilter,
  selectItem,
  updateReviewItem,
  exportReport,
  getRiskColor,
  getRiskLabel
} = useComplianceReview(taskId.value)

// PDF工具栏相关
const fontFamily = ref('宋体')
const fontSize = ref('12')

// 弹窗状态
const checkListVisible = ref(false)

// 编辑状态
const editingItems = ref<string[]>([])

// 计算属性
const fileName = computed(() => reviewTask.value?.fileName || '文档名称')
const reviewTime = computed(() => reviewTask.value?.reviewTime || '2024-01-15 09:30:00')

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}

const handleExport = async () => {
  try {
    const config: ExportConfig = {
      format: 'pdf',
      includeDetails: true,
      includeImages: true
    }
    await exportReport(config)
  } catch (error) {
    console.error('导出失败:', error)
  }
}

const showCheckList = () => {
  checkListVisible.value = true
}

const handleCheckListSave = (checkList: CheckList) => {
  console.log('保存审查清单:', checkList)
  message.success('审查清单已保存')
}

// 切换编辑模式
const toggleEditMode = (itemId: string) => {
  const index = editingItems.value.indexOf(itemId)
  if (index > -1) {
    editingItems.value.splice(index, 1)
  } else {
    editingItems.value.push(itemId)
  }
}

const openDocument = (link: string) => {
  // 在新窗口打开相关文档
  window.open(link, '_blank')
}

// ReviewItem 组件的事件处理方法
const handleItemUpdate = async (item: any) => {
  try {
    await updateReviewItem(item.id, item)
    message.success('更新成功')
  } catch (error) {
    message.error('更新失败')
  }
}

const handleItemVerify = (item: any) => {
  // 核实项目
  message.info('核实功能开发中...')
  console.log('核实项目:', item)
}

const handleItemCancel = (item: any) => {
  // 取消项目或退出编辑模式
  const itemId = item.id
  const index = editingItems.value.indexOf(itemId)
  if (index > -1) {
    editingItems.value.splice(index, 1)
  }
  console.log('取消项目:', item)
}

const handleItemLike = (item: any) => {
  // 点赞
  console.log('点赞项目:', item)
  // 这里可以调用API更新点赞数
}

const handleItemDislike = (item: any) => {
  // 点踩
  console.log('点踩项目:', item)
  // 这里可以调用API更新点踩数
}

const handleItemAction = async (item: any, action: string) => {
  switch (action) {
    case 'edit':
      // 编辑项目
      const itemId = item.id
      if (!editingItems.value.includes(itemId)) {
        editingItems.value.push(itemId)
      }
      break
    case 'locate':
      // 定位到PDF中的位置
      if (item.pageNumber) {
        currentPage.value = item.pageNumber
        selectItem(item)
      }
      break
    default:
      console.log('未知操作:', action)
  }
}

const onPdfLoad = () => {
  console.log('PDF加载完成')
  // PDF加载完成后获取标注信息
  fetchPdfAnnotations()
}

// 初始化数据
const initData = async () => {
  try {
    await Promise.all([
      fetchReviewTask(),
      fetchReviewItems(),
      fetchPdfInfo(taskId.value) // 这里应该传入实际的文件ID
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    message.error('加载数据失败')
  }
}

onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.compliance-review-container {
  color: #111827;
}

.header-section {
  display: flex;
  align-items: center;
  justify-content: space-between; 
  padding: 11px 24px;
  border-bottom: 1px solid var(--line-2); 
  .breadcrumb-area {
    display: flex;
    align-items: center;  
    .nav-buttons {
      display: flex;
      gap: 16px;
      .nav-btn {
        display: flex;
        align-items: center; 
        &.back-btn { 
          padding: 8px 16px;
          border: 1px solid var(--line-3);
          border-radius: 4px;
          .icon {
            margin-right: 8px;
          }
        }
      }
    } 
  }
  
  .info-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    .review-time {
      display: flex;
      align-items: center;
      gap: 10px;
      color: #4B5563;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
      
      .export-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        border: 1px solid var(--line-3);
      }
    }
  }
}

.main-content {
  display: flex;
}
.pdf-reader-wrapper,
.review-panel {
  flex: 1;
  min-width: 300px;
}
.pdf-reader-wrapper { 
  border:1px solid #E5E7EB;
  position: relative;

  .pdf-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
} 
.review-panel { 
  max-width: 832px;
  display: flex;
  flex-direction: column;
  background: var(--fill-0);
  
  .panel-header {
    padding: 16px; 
    font-size: 16px;
  }
  
  .filter-tabs { 
    display: flex; 
    align-items: center; 
    gap: 4px;
    height: 56px;
    padding: 0 4px;
    background-color: #F5F5F5;
    border-radius: 4px;
    margin:0 16px 16px 16px;
    .filter-tab {
      display: flex;
      align-items: center; 
      justify-content: center;
      flex: 1;
      min-width: 60px;
      height: 38px;
      border-radius: 4px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s; 
      .tab-label {
        font-size: var(--font-14);
        color: var(--text-4);
      }
      &:nth-child(2) {
        .tab-count{
          background: #FEE2E2;
          color: #DC2626;
        }
      }
      &:nth-child(3) {
        .tab-count{
          background-color: #DCFCE7;
          color: #16A34A;
        }
      }

      .tab-count {   
        display: inline-block;
        text-align: center; 
        color: #4B5563;
        border-radius: 50%;
        background-color: #F3F4F6;
        margin-left: 8px;
        padding:0 4px; 
        min-width: 20px; 
      }

      &:hover,
      &.active {
        background: var(--fill-0); 
      }  
    }
  }
  
  .review-items { 
    .item-group {
      margin-bottom: 24px;

      .item-title-bar {
        display: flex;
        align-items: center; 
        padding: 12px 16px;
        background: #F5F5F5;
        border-bottom: 1px solid #E5E7EB; 
        .item-index {
          width: 6px;
          height: 16px;
          background-color: var(--main-6);
          border-radius: 2px;
          margin-right: 12px;
        }

        .item-title {
          font-size: var(--font-16); 
        }

        .item-count {
          color: #4B5563;
          border-radius: 50%;
          background-color: #F3F4F6;
          margin-left: 8px;
          padding:0 4px; 
          min-width: 20px; 
        }
      }

      .sub-items {
        border: 1px solid var(--line-2);
        border-top: none;
        border-radius: 0 0 8px 8px;
        overflow: hidden;

        :deep(.review-item) {
          border: none;
          border-radius: 0;
          margin-bottom: 0;

          &:not(:last-child) {
            border-bottom: 1px solid var(--line-2);
          }
        }
      }
    }
  }
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}
</style>
