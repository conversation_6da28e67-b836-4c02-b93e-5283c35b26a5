<template>
  <div class="compliance-review-container">
    <!-- 顶部导航区域 -->
    <div class="header-section">
      <div class="breadcrumb-area">
        <div class="nav-buttons">
          <a-button type="text" class="nav-btn back-btn" @click="goHome">
            <template #icon>
              <CornerUpLeft class="icon" :size="16"/>
            </template>
            首页
          </a-button>
          <a-button type="text" class="nav-btn " @click="goBack">
            <template #icon>
              <Clock8 class="icon" :size="16"/>
            </template>
          </a-button>
        </div>
        <div class="file-name">{{ fileName }}</div>
      </div>
      
      <div class="info-actions">
        <div class="review-time">
          <Calendar1  class="icon" :size="16"/>
          <span>审查时间：{{ reviewTime }}</span>
        </div>
        <div class="action-buttons">
          <a-button class="export-btn" @click="handleExport">
            <template #icon>
              <Download class="icon" :size="16"/>
            </template>
            导出
          </a-button>
          <a-button type="primary"  @click="showCheckList">
            查看审查清单
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="main-content">
      <!-- PDF阅读器区域 -->  
      <div class="pdf-reader-wrapper">
        <PdfReader
          v-if="pdfUrl"
          :url="pdfUrl"
          :page="currentPage"
          :zoom="zoomLevel"
          :rect="highlightRects"
          @load="onPdfLoad"
        />
        <div v-else class="pdf-placeholder">
          <BaseEmpty description="PDF文档加载中..." />
        </div>
      </div> 

      <!-- 审查结果面板 -->
      <div class="review-panel">
        <div class="panel-header">
          <h3>审查结果</h3>
        </div> 
        <div class="filter-tabs">
          <div
            v-for="tab in filterTabs"
            :key="tab.key"
            :class="['filter-tab', tab.key, { active: activeFilter === tab.key }]"
            @click="setActiveFilter(tab.key)"
          >
            <span class="tab-label">{{ tab.label }}</span>
            <span class="tab-count">{{ tab.count }}</span>
          </div>
        </div>

        <!-- 审查项目列表 -->
        <div class="review-items">
          <a-spin :spinning="loading">
            <div v-if="filteredItems.length === 0" class="empty-state">
              <BaseEmpty description="暂无审查项目" />
            </div>
            <div v-else class="items-list">
              <div
                v-for="item in filteredItems"
                :key="item.id"
                :class="['review-item', item.riskLevel]"
              >
                <!-- 项目标题栏 -->
                <div class="item-title-bar">
                  <div class="title-left">
                    <span class="item-index">{{ item.id }}</span>
                    <span class="item-title">{{ item.title }}</span>
                    <span class="item-count">{{ item.subItemCount || 4 }}</span>
                  </div>
                </div>

                <!-- 项目内容 -->
                <div class="item-content">
                  <div
                    v-for="subItem in item.subItems || [item]"
                    :key="subItem.id || item.id"
                    :class="['sub-item', subItem.riskLevel || item.riskLevel]"
                  >
                    <!-- 风险标签 -->
                    <div class="risk-tag">
                      <a-tag :color="getRiskColor(subItem.riskLevel || item.riskLevel)">
                        {{ getRiskLabel(subItem.riskLevel || item.riskLevel) }}
                      </a-tag>
                    </div>

                    <!-- 项目描述 -->
                    <div class="sub-item-description">
                      {{ subItem.description || item.description }}
                    </div>

                    <!-- 风险提示 -->
                    <div v-if="subItem.riskDetails || item.details" class="risk-details">
                      <span class="risk-label">风险提示：</span>
                      <span class="risk-content">{{ subItem.riskDetails || item.details }}</span>
                    </div>

                    <!-- 审查依据 -->
                    <div v-if="subItem.legalBasis || item.legalBasis" class="legal-basis">
                      <span class="basis-label">审查依据：</span>
                      <span class="basis-content">{{ subItem.legalBasis || item.legalBasis }}</span>
                      <a-button type="link" size="small" @click="openDocument(subItem.documentLink || item.documentLink)">
                        <template #icon>
                          <FileTextOutlined />
                        </template>
                      </a-button>
                    </div>

                    <!-- 建议修改 -->
                    <div v-if="subItem.suggestion || item.suggestion" class="suggestion">
                      <span class="suggestion-label">建议修改：</span>
                      <span class="suggestion-content">{{ subItem.suggestion || item.suggestion }}</span>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="item-actions">
                      <div class="action-buttons">
                        <a-button
                          v-if="(subItem.riskLevel || item.riskLevel) === 'risk'"
                          type="primary"
                          size="small"
                          @click="handleItemAction(subItem || item, 'verify')"
                        >
                          核实
                        </a-button>
                        <a-button
                          size="small"
                          @click="handleItemAction(subItem || item, 'cancel')"
                        >
                          取消
                        </a-button>
                      </div>
                      <div class="action-stats">
                        <span class="like-count">
                          <a-button type="text" size="small">
                            👍 {{ subItem.likeCount || 0 }}
                          </a-button>
                        </span>
                        <span class="dislike-count">
                          <a-button type="text" size="small">
                            👎 {{ subItem.dislikeCount || 0 }}
                          </a-button>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-spin>
        </div>
      </div>
    </div>

    <!-- 审查清单弹窗 -->
    <CheckListModal
      v-model:open="checkListVisible"
      :task-id="taskId"
      @save="handleCheckListSave"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ZoomInOutlined,
  ZoomOutOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import { Clock8,CornerUpLeft, Calendar1,Download } from 'lucide-vue-next'
import PdfReader from '@/components/PdfReader/index.vue'
import BaseEmpty from '@/components/BaseEmpty/index.vue'
import CheckListModal from './components/CheckListModal.vue'
import { useComplianceReview } from '@/composables/useComplianceReview'
import type { ExportConfig, CheckList } from '@/types/compliance'

defineOptions({
  name: 'ComplianceReview'
})

const router = useRouter()
const route = useRoute()

// 获取任务ID
const taskId = ref(route.query.taskId as string || '1')

// 使用合规性审查组合式函数
const {
  loading,
  reviewTask,
  reviewItems,
  selectedItem,
  activeFilter,
  pdfUrl,
  currentPage,
  zoomLevel,
  highlightRects,
  filterTabs,
  filteredItems,
  fetchReviewTask,
  fetchReviewItems,
  fetchPdfInfo,
  fetchPdfAnnotations,
  setActiveFilter,
  selectItem,
  updateReviewItem,
  exportReport,
  getRiskColor,
  getRiskLabel
} = useComplianceReview(taskId.value)

// PDF工具栏相关
const fontFamily = ref('宋体')
const fontSize = ref('12')

// 弹窗状态
const checkListVisible = ref(false)

// 计算属性
const fileName = computed(() => reviewTask.value?.fileName || '文档名称')
const reviewTime = computed(() => reviewTask.value?.reviewTime || '2024-01-15 09:30:00')

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}

const handleExport = async () => {
  try {
    const config: ExportConfig = {
      format: 'pdf',
      includeDetails: true,
      includeImages: true
    }
    await exportReport(config)
  } catch (error) {
    console.error('导出失败:', error)
  }
}

const showCheckList = () => {
  checkListVisible.value = true
}

const handleCheckListSave = (checkList: CheckList) => {
  console.log('保存审查清单:', checkList)
  message.success('审查清单已保存')
}

const openDocument = (link: string) => {
  // 在新窗口打开相关文档
  window.open(link, '_blank')
}

const handleItemAction = async (item: any, action: string) => {
  switch (action) {
    case 'edit':
      // 编辑项目
      message.info('编辑功能开发中...')
      break
    case 'locate':
      // 定位到PDF中的位置
      if (item.pageNumber) {
        currentPage.value = item.pageNumber
        selectItem(item)
      }
      break
    default:
      console.log('未知操作:', action)
  }
}

const onPdfLoad = () => {
  console.log('PDF加载完成')
  // PDF加载完成后获取标注信息
  fetchPdfAnnotations()
}

// 初始化数据
const initData = async () => {
  try {
    await Promise.all([
      fetchReviewTask(),
      fetchReviewItems(),
      fetchPdfInfo(taskId.value) // 这里应该传入实际的文件ID
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
    message.error('加载数据失败')
  }
}

onMounted(() => {
  initData()
})
</script>

<style lang="scss" scoped>
.compliance-review-container {
  color: #111827;
}

.header-section {
  display: flex;
  align-items: center;
  justify-content: space-between; 
  padding: 11px 24px;
  border-bottom: 1px solid var(--line-2); 
  .breadcrumb-area {
    display: flex;
    align-items: center;  
    .nav-buttons {
      display: flex;
      gap: 16px;
      .nav-btn {
        display: flex;
        align-items: center; 
        &.back-btn { 
          padding: 8px 16px;
          border: 1px solid var(--line-3);
          border-radius: 4px;
          .icon {
            margin-right: 8px;
          }
        }
      }
    } 
  }
  
  .info-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    .review-time {
      display: flex;
      align-items: center;
      gap: 10px;
      color: #4B5563;
    }
    
    .action-buttons {
      display: flex;
      gap: 10px;
      
      .export-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        border: 1px solid var(--line-3);
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}
 
.pdf-reader-wrapper {
  flex: 1;
  border:1px solid #E5E7EB;
  position: relative;

  .pdf-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
} 
.review-panel {
  width: 400px;
  display: flex;
  flex-direction: column;
  background: var(--fill-0);
  
  .panel-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--line-2);
    
    h3 {
      margin: 0;
      font-size: var(--font-16);
      font-weight: 500;
      color: var(--text-5);
    }
  }
  
  .filter-tabs {
    padding: 16px 24px;
    display: flex;
    gap: 16px;
    border-bottom: 1px solid var(--line-2);
    background: var(--fill-0);

    .filter-tab {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
      background: var(--fill-1);
      border: 1px solid var(--line-2);

      .tab-label {
        font-size: var(--font-14);
        color: var(--text-4);
      }

      .tab-count {
        background: var(--fill-4);
        color: var(--text-0);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: var(--font-12);
        min-width: 20px;
        text-align: center;
      }

      &:hover {
        border-color: var(--main-6);
      }

      &.active {
        background: var(--main-1);
        border-color: var(--main-6);

        .tab-label {
          color: var(--main-6);
          font-weight: 500;
        }

        .tab-count {
          background: var(--main-6);
          color: var(--text-0);
        }
      }

      &.risk {
        &.active {
          .tab-count {
            background: var(--error-6);
          }
        }
      }

      &.safe {
        &.active {
          .tab-count {
            background: var(--success-6);
          }
        }
      }

      &.na {
        &.active {
          .tab-count {
            background: var(--neutral-6);
          }
        }
      }
    }
  }
  
  .review-items {
    flex: 1;
    overflow-y: auto;
    
    .items-list {
      padding: 16px 24px;
    }
    
    .review-item {
      margin-bottom: 16px;
      border: 1px solid var(--line-2);
      border-radius: 8px;
      background: var(--fill-0);
      overflow: hidden;

      .item-title-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: var(--fill-1);
        border-bottom: 1px solid var(--line-2);

        .title-left {
          display: flex;
          align-items: center;
          gap: 8px;

          .item-index {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background: var(--main-6);
            color: var(--text-0);
            border-radius: 4px;
            font-size: var(--font-12);
            font-weight: 500;
          }

          .item-title {
            font-size: var(--font-14);
            font-weight: 500;
            color: var(--text-5);
          }

          .item-count {
            background: var(--fill-4);
            color: var(--text-0);
            padding: 2px 6px;
            border-radius: 10px;
            font-size: var(--font-12);
          }
        }
      }

      .item-content {
        .sub-item {
          padding: 16px;
          border-bottom: 1px solid var(--line-2);

          &:last-child {
            border-bottom: none;
          }

          &.risk {
            border-left: 4px solid var(--error-6);
          }

          &.safe {
            border-left: 4px solid var(--success-6);
          }

          &.na {
            border-left: 4px solid var(--neutral-6);
          }

          .risk-tag {
            margin-bottom: 12px;
          }

          .sub-item-description {
            font-size: var(--font-14);
            color: var(--text-5);
            font-weight: 500;
            margin-bottom: 12px;
            line-height: 1.5;
          }

          .risk-details {
            margin-bottom: 12px;

            .risk-label {
              color: var(--error-6);
              font-weight: 500;
              font-size: var(--font-14);
            }

            .risk-content {
              color: var(--text-4);
              font-size: var(--font-14);
              line-height: 1.5;
            }
          }

          .legal-basis {
            margin-bottom: 12px;
            display: flex;
            align-items: flex-start;
            gap: 8px;

            .basis-label {
              color: var(--text-5);
              font-weight: 500;
              font-size: var(--font-14);
              flex-shrink: 0;
            }

            .basis-content {
              color: var(--text-4);
              font-size: var(--font-14);
              line-height: 1.5;
              flex: 1;
            }
          }

          .suggestion {
            margin-bottom: 16px;
            padding: 8px 12px;
            background: var(--warning-1);
            border-radius: 4px;
            border-left: 3px solid var(--warning-6);

            .suggestion-label {
              color: var(--warning-7);
              font-weight: 500;
              font-size: var(--font-14);
            }

            .suggestion-content {
              color: var(--warning-8);
              font-size: var(--font-14);
            }
          }

          .item-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .action-buttons {
              display: flex;
              gap: 8px;
            }

            .action-stats {
              display: flex;
              align-items: center;
              gap: 16px;

              .like-count,
              .dislike-count {
                display: flex;
                align-items: center;
                gap: 4px;
                color: var(--text-3);
                font-size: var(--font-12);

                .ant-btn {
                  padding: 0;
                  height: auto;
                  border: none;
                  box-shadow: none;

                  &:hover {
                    color: var(--main-6);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}
</style>
